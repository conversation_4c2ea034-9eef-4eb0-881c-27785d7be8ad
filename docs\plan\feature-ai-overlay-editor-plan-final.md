# AI 覆盖层编辑器功能扩展计划（最终版）

**目标**: 实现 AI 覆盖层的小编辑器能够根据主侧边栏的选择，**显示并编辑**“大纲”、“设定”、“角色”和“知识库”的内容。

**核心思路**: 完全复制和扩展当前已成功实现的“章节”同步模式到其他所有数据类型上，以保证代码风格统一和开发效率。

**行动计划**:

---

### **第一部分：升级 `AIOverlayPanel.tsx` 组件**

**文件**: `src/components/works/AIOverlayPanel.tsx`

#### **1. 扩展组件接口 (`AIOverlayPanelProps`)**

*   **目的**: 使组件能够接收来自父组件的所有必要数据和回调函数。
*   **操作**: 扩展 `AIOverlayPanelProps` 接口，增加以下属性：
    *   `editMode`: `'chapter' | 'character' | 'outline' | 'setting' | 'knowledge'`
    *   `activeItemId`: `string | number | null`
    *   `outlines`: `Outline[]`
    *   `settings`: `Setting[]`
    *   `characters`: `Character[]`
    *   `knowledges`: `Knowledge[]`
    *   `onContentChange`: `(newContent: string) => void`

#### **2. 实现状态同步与内容切换**

*   **目的**: 让组件内部状态能实时响应外部 props 的变化，并正确地在小编辑器中显示对应内容。
*   **操作**:
    1.  在 `AIOverlayPanel` 组件函数体顶部，解构出所有新增的 props。
    2.  新增一个名为 `currentEditorContent` 的内部 state，用于存放当前小编辑器应显示的内容。
    3.  **关键**：新增一个 `useEffect` 钩子，该钩子依赖于 `[editMode, activeItemId, chapters, outlines, settings, characters, knowledges, activeChapter]`。
    4.  在此 `useEffect` 内部，编写一个 `if/else` 或 `switch` 逻辑：
        *   如果 `editMode` 是 `'chapter'`，则从 `chapters` 数组中找到 `activeChapter` 对应的章节内容。
        *   如果 `editMode` 是 `'outline'`，则从 `outlines` 数组中找到 `activeItemId` 对应的大纲内容。
        *   对 `'setting'`, `'character'`, `'knowledge'` 执行类似操作。
        *   **特别注意**：对于 `'character'` 类型，需要将角色的多个字段（如性别、性格、背景）拼接成一个格式化的字符串。
        *   将找到的内容更新到 `currentEditorContent` state 中。

#### **3. 连接编辑器**

*   **目的**: 将小编辑器与我们新的状态管理逻辑完全打通，实现双向绑定。
*   **操作**:
    1.  找到小编辑器的 `<textarea>` 元素。
    2.  将其 `value` 属性绑定到 `currentEditorContent` state。
    3.  将其 `onChange` 事件处理函数修改为：
        ```javascript
        (e) => {
          const newContent = e.target.value;
          setCurrentEditorContent(newContent); // 更新内部显示
          onContentChange(newContent);      // 通过回调通知父组件内容已改变
        }
        ```

---

### **第二部分：升级 `page.tsx` 组件**

**文件**: `src/app/works/[id]/page.tsx`

#### **1. 传递 Props**

*   **目的**: 将主页面的状态和回调函数“喂”给 `AIOverlayPanel` 组件。
*   **操作**:
    1.  找到 `<AIOverlayPanel ... />` 的调用位置。
    2.  添加在第一部分中定义的所有新 props，并将页面中对应的 state 和回调函数传递过去。例如：
        *   `editMode={editMode}`
        *   `activeItemId={...}` (这里需要一个三元运算符或 `if/else` 逻辑来根据 `editMode` 传递正确的 ID，如 `editMode === 'outline' ? activeOutlineId : ...`)
        *   `outlines={outlines}`
        *   `settings={settings}`
        *   ...等等
        *   `onContentChange={handleUnifiedContentChange}`

---

**执行建议**:

请将此计划交给一个拥有代码修改权限的角色执行。建议按顺序先完成 **第一部分** 对 `AIOverlayPanel.tsx` 的修改，再完成 **第二部分** 对 `page.tsx` 的修改，以避免因 props 不匹配导致的临时性编译错误。